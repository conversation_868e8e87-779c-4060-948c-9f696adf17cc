'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { GraduationCap, Award, Calendar } from 'lucide-react';
import { education } from '@/data/education';

const Education = () => {
  return (
    <section id="education" className="py-20 bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ y: 50, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl sm:text-5xl font-bold mb-4">
            Education & <span className="python-yellow">Certifications</span>
          </h2>
          <div className="w-24 h-1 bg-python-blue mx-auto mb-4"></div>
          <p className="text-gray-400 max-w-2xl mx-auto">
            My academic background and professional certifications
          </p>
        </motion.div>

        <div className="max-w-4xl mx-auto">
          <div className="grid md:grid-cols-2 gap-6">
            {education.map((item, index) => (
              <motion.div
                key={item.id}
                className="bg-gray-800/50 rounded-xl p-6 border border-gray-700 hover:border-python-blue/50 transition-all duration-300 group"
                initial={{ y: 50, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.02, y: -5 }}
              >
                {/* Icon */}
                <motion.div
                  className="flex justify-center mb-4"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ duration: 0.3 }}
                >
                  {item.type === 'certification' ? (
                    <div className="p-3 bg-python-yellow/10 rounded-full">
                      <Award size={32} className="text-python-yellow" />
                    </div>
                  ) : (
                    <div className="p-3 bg-python-blue/10 rounded-full">
                      <GraduationCap size={32} className="text-python-blue" />
                    </div>
                  )}
                </motion.div>

                {/* Content */}
                <div className="text-center">
                  <h3 className="text-xl font-bold text-white mb-2 group-hover:text-python-yellow transition-colors">
                    {item.degree}
                  </h3>
                  
                  <p className="text-gray-300 mb-3">
                    {item.institution}
                  </p>
                  
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-400">
                    <Calendar size={14} />
                    <span>{item.year}</span>
                  </div>
                </div>

                {/* Hover Effect */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-python-blue/5 to-python-yellow/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
                  initial={{ opacity: 0 }}
                  whileHover={{ opacity: 1 }}
                />
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Education;