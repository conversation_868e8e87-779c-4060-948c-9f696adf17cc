'use client';

import React from 'react';
import { motion } from 'framer-motion';

const About = () => {
  const skills = {
    languages: ['Python', 'SQL', 'JavaScript'],
    frameworks: ['Scrapy', 'Flask', 'Django', 'Beautiful Soup', 'Selenium', 'OpenCV', 'Easy-OCR'],
    databases: ['MySQL', 'MSSQL', 'MongoDB'],
    tools: ['Git', 'JIRA', 'AWS', 'Appsmith', 'Google Data Studio'],
    others: ['XPath', 'JSON', 'Pandas', 'GA4 Analytics']
  };

  const skillCategories = [
    { title: 'Languages', items: skills.languages, color: 'border-python-blue' },
    { title: 'Frameworks & Libraries', items: skills.frameworks, color: 'border-python-yellow' },
    { title: 'Databases', items: skills.databases, color: 'border-green-500' },
    { title: 'Tools', items: skills.tools, color: 'border-purple-500' },
    { title: 'Others', items: skills.others, color: 'border-orange-500' }
  ];

  return (
    <section id="about" className="py-20 bg-gray-900/50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ y: 50, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl sm:text-5xl font-bold mb-4">
            About <span className="python-yellow">Me</span>
          </h2>
          <div className="w-24 h-1 bg-python-blue mx-auto"></div>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center max-w-6xl mx-auto">
          {/* Left Side - Portrait */}
          <motion.div
            className="flex justify-center lg:justify-end"
            initial={{ x: -100, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="relative">
              <motion.div
                className="w-80 h-96 bg-gradient-to-br from-python-blue to-python-yellow rounded-2xl p-1"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.3 }}
              >
                <div className="w-full h-full bg-gray-800 rounded-2xl flex items-center justify-center">
                  <div className="text-8xl font-bold text-white">NG</div>
                </div>
              </motion.div>
              
              {/* Floating elements */}
              <motion.div
                className="absolute -top-4 -right-4 w-12 h-12 bg-python-yellow rounded-full"
                animate={{
                  scale: [1, 1.2, 1],
                  rotate: [0, 180, 360]
                }}
                transition={{ duration: 4, repeat: Infinity }}
              />
              <motion.div
                className="absolute -bottom-4 -left-4 w-8 h-8 bg-python-blue rounded-full"
                animate={{
                  scale: [1, 1.3, 1],
                  rotate: [360, 180, 0]
                }}
                transition={{ duration: 3, repeat: Infinity }}
              />
            </div>
          </motion.div>

          {/* Right Side - Bio */}
          <motion.div
            className="space-y-8"
            initial={{ x: 100, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div>
              <h3 className="text-2xl font-bold mb-4 python-blue">
                Python Developer & Data Scraping Expert
              </h3>
              <p className="text-gray-300 text-lg leading-relaxed">
                I'm a Python Developer with 4+ years of experience specializing in web scraping, 
                data extraction, backend development, and process automation. With hands-on expertise 
                in tools like Scrapy, Selenium, BeautifulSoup, Flask, and OpenCV, I build efficient, 
                scalable scraping systems and backend APIs. I'm passionate about transforming 
                unstructured data into structured insights and streamlining workflows through automation.
              </p>
            </div>

            <div>
              <h4 className="text-xl font-bold mb-6 python-yellow">
                Technical Skills
              </h4>
              <div className="space-y-6">
                {skillCategories.map((category, index) => (
                  <motion.div
                    key={category.title}
                    initial={{ y: 30, opacity: 0 }}
                    whileInView={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <h5 className="text-sm font-semibold text-gray-400 mb-2">
                      {category.title}
                    </h5>
                    <div className="flex flex-wrap gap-2">
                      {category.items.map((skill, skillIndex) => (
                        <motion.span
                          key={skill}
                          className={`px-3 py-1 text-sm border rounded-full bg-gray-800/50 text-gray-200 hover:text-white transition-colors ${category.color}`}
                          whileHover={{ scale: 1.05, y: -2 }}
                          initial={{ opacity: 0, scale: 0.8 }}
                          whileInView={{ opacity: 1, scale: 1 }}
                          transition={{ duration: 0.3, delay: skillIndex * 0.05 }}
                          viewport={{ once: true }}
                        >
                          {skill}
                        </motion.span>
                      ))}
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default About;