'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Briefcase, Calendar, MapPin } from 'lucide-react';
import { experience } from '@/data/experience';

const Experience = () => {
  return (
    <section id="experience" className="py-20 bg-gray-900/50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ y: 50, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl sm:text-5xl font-bold mb-4">
            Professional <span className="python-yellow">Experience</span>
          </h2>
          <div className="w-24 h-1 bg-python-blue mx-auto mb-4"></div>
          <p className="text-gray-400 max-w-2xl mx-auto">
            My journey through various roles in Python development and data automation
          </p>
        </motion.div>

        <div className="max-w-4xl mx-auto">
          {/* Timeline */}
          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-4 md:left-1/2 transform md:-translate-x-0.5 top-0 bottom-0 w-0.5 bg-python-blue/30"></div>

            {experience.map((job, index) => (
              <motion.div
                key={job.id}
                className="relative flex items-start mb-12 last:mb-0"
                initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
              >
                {/* Timeline Dot */}
                <motion.div
                  className="absolute left-4 md:left-1/2 transform md:-translate-x-1/2 w-3 h-3 bg-python-blue rounded-full border-4 border-background z-10"
                  whileHover={{ scale: 1.5 }}
                  transition={{ duration: 0.2 }}
                />

                {/* Content Card */}
                <motion.div
                  className={`w-full md:w-5/12 ml-12 md:ml-0 ${
                    index % 2 === 0 ? 'md:mr-auto md:text-right' : 'md:ml-auto md:text-left'
                  }`}
                  whileHover={{ scale: 1.02, y: -5 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="bg-gray-800/50 rounded-xl p-6 border border-gray-700 hover:border-python-blue/50 transition-all duration-300 group">
                    {/* Company & Duration */}
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-3">
                      <h3 className="text-lg font-bold text-python-yellow group-hover:text-python-blue transition-colors">
                        {job.company}
                      </h3>
                      <div className="flex items-center gap-2 text-sm text-gray-400 mt-1 md:mt-0">
                        <Calendar size={14} />
                        <span>{job.duration}</span>
                      </div>
                    </div>

                    {/* Position */}
                    <h4 className="text-xl font-semibold text-white mb-2">
                      {job.position}
                    </h4>

                    {/* Location */}
                    <div className="flex items-center gap-2 text-gray-400 mb-4">
                      <MapPin size={14} />
                      <span>{job.location}</span>
                    </div>

                    {/* Description */}
                    <p className="text-gray-300 leading-relaxed">
                      {job.description}
                    </p>

                    {/* Briefcase Icon */}
                    <motion.div
                      className="mt-4 flex justify-end"
                      initial={{ rotate: 0 }}
                      whileHover={{ rotate: 15 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Briefcase size={24} className="text-python-blue/50" />
                    </motion.div>
                  </div>
                </motion.div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Experience;