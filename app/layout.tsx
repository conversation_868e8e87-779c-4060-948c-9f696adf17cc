import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Nikunj <PERSON>era - Python Developer | Data Scraping Master',
  description: 'Python Developer with 4+ years of experience specializing in web scraping, data extraction, backend development, and process automation. Expert in Scrapy, Selenium, Flask, and more.',
  keywords: 'Python Developer, Web Scraping, Data Extraction, Scrapy, Selenium, Flask, Backend Development, Automation',
  authors: [{ name: '<PERSON><PERSON><PERSON>' }],
  viewport: 'width=device-width, initial-scale=1',
  robots: 'index, follow',
  openGraph: {
    title: 'Nikunj <PERSON>era - Python Developer | Data Scraping Master',
    description: 'Python Developer with 4+ years of experience specializing in web scraping, data extraction, backend development, and process automation.',
    type: 'website',
    locale: 'en_US',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${inter.className} bg-background text-foreground antialiased`}>
        {children}
      </body>
    </html>
  );
}