export const projects = [
  {
    id: 1,
    title: "Jaher Notice Automation",
    description: "Automated public notice extraction using OCR and web scraping. Flask API to deliver structured data.",
    techStack: ["Flask", "Selenium", "bs4", "OpenCV", "MSSQL"],
    github: "#",
    demo: "#"
  },
  {
    id: 2,
    title: "Land Record Automation (Proplegit.com)",
    description: "Automated land record scraping with OCR processing. Integrated with dynamic API for frontend display.",
    techStack: ["Flask", "Selenium", "bs4", "Easy-OCR", "MSSQL"],
    github: "#",
    demo: "#"
  },
  {
    id: 3,
    title: "Doctor's, Jobs & Industrial Data Extraction",
    description: "Multi-source crawling to collect domain-specific records. Exported structured datasets for business use.",
    techStack: ["Scrapy", "bs4", "Selenium", "JSON", "CSV"],
    github: "#",
    demo: "#"
  },
  {
    id: 4,
    title: "Ironlist Global Scraping",
    description: "Built a global construction equipment scraper. Led maintenance and trained new developers.",
    techStack: ["Scrapy", "Selenium", "MySQL"],
    github: "#",
    demo: "#"
  }
];